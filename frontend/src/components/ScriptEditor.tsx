"use client";

import { useState, useCallback } from "react";
import { Paragraph, ParagraphType } from "@/types";

interface ScriptEditorProps {
  paragraphs: Paragraph[];
  onChange: (paragraphs: Paragraph[]) => void;
}

export default function ScriptEditor({
  paragraphs,
  onChange,
}: ScriptEditorProps) {
  const [selectedLineType, setSelectedLineType] =
    useState<ParagraphType>("lines");
  //const [isApplyingLayout, setIsApplyingLayout] = useState(false);

  // const handleLineChange = useCallback(
  //   (lineId: string, field: keyof ScriptLine, value: any) => {
  //     const updatedLines = lines.map((line) =>
  //       line.line_id === lineId ? { ...line, [field]: value } : line
  //     );
  //     onChange(updatedLines);
  //   },
  //   [lines, onChange]
  // );

  const handleUpdateParagraph = useCallback(
    (index: number, field: keyof Paragraph, value: any) => {
      const updatedParagraphs = paragraphs.map((paragraph, i) =>
        i === index ? { ...paragraph, [field]: value } : paragraph
      );
      onChange(updatedParagraphs);
    },
    [paragraphs, onChange]
  );

  // const handleAddLine = useCallback(() => {
  //   // より確実な一意ID生成
  //   const generateId = () => {
  //     const timestamp = Date.now();
  //     const random = Math.random().toString(36).substring(2, 15);
  //     return `line-${timestamp}-${random}`;
  //   };

  //   const newLine: ScriptLine = {
  //     line_id: generateId(),
  //     line_type: selectedLineType,
  //     text: "",
  //     has_unreadable_chars: false,
  //   };
  //   onChange([...lines, newLine]);
  // }, [lines, onChange, selectedLineType]);

  const handleAddParagraph = useCallback(() => {
    const newParagraph: Paragraph = {
      type: selectedLineType,
      header: "",
      content: "",
    };
    onChange([...paragraphs, newParagraph]);
  }, [paragraphs, onChange, selectedLineType]);

  // const handleDeleteLine = useCallback(
  //   (lineId: string) => {
  //     // 削除前の安全チェック
  //     if (!lineId || lines.length === 0) {
  //       console.warn("削除対象の行が見つかりません");
  //       return;
  //     }

  //     const updatedLines = lines.filter((line) => line.line_id !== lineId);

  //     // 削除後の配列が空でないことを確認
  //     if (updatedLines.length === 0) {
  //       console.log("すべての行が削除されました");
  //     }

  //     onChange(updatedLines);
  //   },
  //   [lines, onChange]
  // );

  const handleDeleteParagraph = useCallback(
    (index: number) => {
      // 削除前の安全チェック
      if (index < 0 || index >= paragraphs.length) {
        console.warn("削除対象の段落が見つかりません");
        return;
      }

      const updatedParagraphs = paragraphs.filter((_, i) => i !== index);

      // 削除後の配列が空でないことを確認
      if (updatedParagraphs.length === 0) {
        console.log("すべての段落が削除されました");
      }

      onChange(updatedParagraphs);
    },
    [paragraphs, onChange]
  );

  // const handleApplyLayout = useCallback(async () => {
  //   if (isApplyingLayout) return

  //   setIsApplyingLayout(true)
  //   try {
  //     const response = await fetch('/api/apply-layout', {
  //       method: 'POST',
  //       headers: {
  //         'Content-Type': 'application/json',
  //       },
  //       body: JSON.stringify({ lines }),
  //     })

  //     if (!response.ok) {
  //       throw new Error('レイアウト適用に失敗しました')
  //     }

  //     const { lines: layoutAppliedLines } = await response.json()
  //     onChange(layoutAppliedLines)
  //   } catch (error) {
  //     console.error('レイアウト適用エラー:', error)
  //     alert('レイアウトの適用に失敗しました。')
  //   } finally {
  //     setIsApplyingLayout(false)
  //   }
  // }, [lines, onChange, isApplyingLayout])

  const getLineTypeLabel = (type: ParagraphType) => {
    switch (type) {
      case "lines":
        return "台詞";
      case "lyrics":
        return "歌";
      case "directions":
        return "ト書き";
      default:
        return "不明";
    }
  };

  if (paragraphs.length === 0) {
    return (
      <div className="py-12 text-center text-muted-foreground">
        <div className="mb-4 text-4xl">📝</div>
        <p>PDFを解析すると、編集可能なテキストがここに表示されます</p>
        <p className="mt-2 text-sm">「解析開始」ボタンをクリックしてください</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* 操作ボタン */}
      <div className="flex justify-center gap-4">
        <button
          onClick={handleAddParagraph}
          className="px-4 py-2 font-medium rounded-lg bg-primary text-primary-foreground hover:bg-primary/90"
        >
          + 行追加
        </button>
        {/* <button
          onClick={handleApplyLayout}
          disabled={isApplyingLayout}
          className={`px-4 py-2 rounded-lg font-medium transition-colors ${
            isApplyingLayout
              ? 'bg-muted text-muted-foreground cursor-not-allowed'
              : 'bg-green-600 text-white hover:bg-green-700'
          }`}
        >
          {isApplyingLayout ? '適用中...' : '📐 スタイル適用'}
        </button> */}
      </div>

      {/* 縦書きグリッド */}
      <div className="overflow-hidden border rounded-lg border-border bg-muted">
        <div className="max-h-[700px] overflow-auto">
          {/* 縦書きグリッド：右から左へ列が並ぶ */}
          <div className="flex flex-row-reverse gap-3 p-6 min-w-max">
            {paragraphs.map((paragraph, index) => {
              // デバッグ: 重複IDチェック
              // const duplicateIds = lines.filter(
              //   (l) => l.line_id === paragraph.line_id
              // );
              // if (duplicateIds.length > 1) {
              //   console.warn(
              //     `重複ID発見: ${paragraph.line_id} (${duplicateIds.length}個)`
              //   );
              // }
              const has_unreadable_chars = paragraph.content.includes("●");

              return (
                <div
                  key={index}
                  className={`flex flex-col border-2 rounded-lg p-2 min-h-[600px] w-32 shadow-sm ${
                    has_unreadable_chars
                      ? "bg-yellow-500/10 border-yellow-500/30"
                      : "bg-card border-border"
                  }`}
                >
                  {/* 列番号 */}
                  <div className="py-1 mb-2 text-sm font-bold text-center rounded text-muted-foreground bg-secondary">
                    {index + 1}
                  </div>

                  {/* 役者名（縦書き） */}
                  <div className="mb-3">
                    <textarea
                      value={paragraph.header || ""}
                      onChange={(e) =>
                        handleUpdateParagraph(index, "header", e.target.value)
                      }
                      className="w-full h-16 px-1 py-1 text-sm font-medium border-2 rounded-md resize-none bg-primary/10 border-primary/20 focus:border-primary focus:outline-none text-card-foreground"
                      placeholder="役者名"
                      style={{
                        writingMode: "vertical-rl",
                        textOrientation: "upright",
                        lineHeight: "1.4",
                      }}
                    />
                  </div>

                  {/* セリフ（縦書き） */}
                  <div className="flex-1 mb-2">
                    <div className="relative">
                      <textarea
                        value={paragraph.content}
                        onChange={(e) =>
                          handleUpdateParagraph(
                            index,
                            "content",
                            e.target.value
                          )
                        }
                        className={`w-full h-80 text-sm border-2 rounded-md px-2 py-2 resize-none font-medium text-card-foreground ${
                          has_unreadable_chars
                            ? "bg-yellow-500/10 border-yellow-500/30"
                            : "bg-green-500/10 border-green-500/20"
                        } focus:outline-none focus:border-green-500`}
                        placeholder="セリフ"
                        style={{
                          writingMode: "vertical-rl",
                          textOrientation: "upright",
                          lineHeight: "1.6",
                          letterSpacing: "0.05em",
                        }}
                      />
                      {has_unreadable_chars && (
                        <div className="absolute -top-1 -right-1 bg-yellow-500 text-white text-xs px-1 py-0.5 rounded-full">
                          ⚠️
                        </div>
                      )}
                    </div>
                  </div>

                  {/* タイプ選択（ドロップダウン） */}
                  <div className="mb-2">
                    <select
                      value={paragraph.type}
                      onChange={(e) =>
                        handleUpdateParagraph(
                          index,
                          "type",
                          e.target.value as ParagraphType
                        )
                      }
                      className="w-full px-1 py-1 text-xs text-center border rounded bg-secondary text-muted-foreground border-border focus:outline-none focus:border-primary"
                    >
                      <option value="lines">台詞</option>
                      <option value="lyrics">歌詞</option>
                      <option value="directions">ト書き</option>
                    </select>
                  </div>

                  {/* 削除ボタン */}
                  <div>
                    <button
                      onClick={() => handleDeleteParagraph(index)}
                      className="w-full py-1 text-xs transition-colors border rounded-md text-destructive hover:text-destructive-foreground hover:bg-destructive border-destructive/30 hover:border-destructive"
                    >
                      削除
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}
